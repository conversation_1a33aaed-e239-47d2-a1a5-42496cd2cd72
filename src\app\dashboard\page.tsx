'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useAuth } from '@/components/providers/AuthProvider'
import { getBlogPosts, getProjects, getUploadedFiles, getEngagementAnalytics } from '@/lib/firebase-operations'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
// import AnalyticsDashboard from '@/components/dashboard/AnalyticsDashboard'
import {
  DocumentTextIcon,
  RocketLaunchIcon,
  PhotoIcon,
  PlusIcon,
  ArrowRightIcon,
  EyeIcon,
  ChatBubbleLeftIcon,
  HeartIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

export default function DashboardPage() {
  const { user } = useAuth()

  const [stats, setStats] = useState({
    totalPosts: 0,
    publishedPosts: 0,
    totalProjects: 0,
    publishedProjects: 0,
    totalFiles: 0,
  })
  const [analytics, setAnalytics] = useState({
    totalComments: 0,
    totalHearts: 0,
    totalViews: 0,
  })
  const [loading, setLoading] = useState(true)
  const [showAnalytics, setShowAnalytics] = useState(false)

  const loadStats = useCallback(async () => {
    if (!user) return

    try {
      const [posts, projects, files, engagementData] = await Promise.all([
        getBlogPosts(user.uid),
        getProjects(user.uid),
        getUploadedFiles(user.uid),
        getEngagementAnalytics()
      ])

      setStats({
        totalPosts: posts.length,
        publishedPosts: posts.filter(post => post.published).length,
        totalProjects: projects.length,
        publishedProjects: projects.filter(project => project.published).length,
        totalFiles: files.length,
      })

      setAnalytics({
        totalComments: engagementData.totalComments,
        totalHearts: engagementData.totalHearts,
        totalViews: engagementData.totalViews,
      })
    } catch (error) {
      console.error('Error loading stats:', error)
    } finally {
      setLoading(false)
    }
  }, [user])

  useEffect(() => {
    if (user) {
      loadStats()
    }
  }, [user, loadStats])

  if (showAnalytics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => setShowAnalytics(false)}
            className="flex items-center gap-2"
          >
            <ArrowRightIcon className="w-4 h-4 rotate-180" />
            Back to Overview
          </Button>
        </div>
        <div className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Analytics Dashboard</h2>
          <p className="text-muted-foreground">Analytics dashboard will be loaded here.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8 min-h-screen bg-white dark:bg-black">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight dashboard-text">
            Welcome to Your Dashboard
          </h1>
          <p className="dashboard-muted mt-1">
            Manage your blog posts, projects, and media files.
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2 dashboard-border">
          <ChartBarIcon className="w-4 h-4" />
          Live Data
        </Badge>
      </div>

      {/* Engagement Overview */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <DashboardCard className="border-l-4 border-l-blue-500">
            <DashboardCardHeader className="pb-2">
              <DashboardCardTitle className="text-sm font-medium dashboard-muted flex items-center gap-2">
                <EyeIcon className="w-4 h-4" />
                Total Views
              </DashboardCardTitle>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="text-2xl font-bold dashboard-text">{analytics.totalViews.toLocaleString()}</div>
              <p className="text-xs dashboard-muted mt-1">
                Across all content
              </p>
            </DashboardCardContent>
          </DashboardCard>

          <DashboardCard className="border-l-4 border-l-green-500">
            <DashboardCardHeader className="pb-2">
              <DashboardCardTitle className="text-sm font-medium dashboard-muted flex items-center gap-2">
                <ChatBubbleLeftIcon className="w-4 h-4" />
                Comments
              </DashboardCardTitle>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="text-2xl font-bold dashboard-text">{analytics.totalComments}</div>
              <p className="text-xs dashboard-muted mt-1">
                Community engagement
              </p>
            </DashboardCardContent>
          </DashboardCard>

          <DashboardCard className="border-l-4 border-l-red-500">
            <DashboardCardHeader className="pb-2">
              <DashboardCardTitle className="text-sm font-medium dashboard-muted flex items-center gap-2">
                <HeartIcon className="w-4 h-4" />
                Hearts
              </DashboardCardTitle>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="text-2xl font-bold dashboard-text">{analytics.totalHearts}</div>
              <p className="text-xs dashboard-muted mt-1">
                Reactions received
              </p>
            </DashboardCardContent>
          </DashboardCard>

          <DashboardCard className="border-l-4 border-l-purple-500">
            <DashboardCardHeader className="pb-2">
              <DashboardCardTitle className="text-sm font-medium dashboard-muted flex items-center gap-2">
                <ChartBarIcon className="w-4 h-4" />
                Engagement
              </DashboardCardTitle>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="text-2xl font-bold dashboard-text">
                {analytics.totalViews > 0
                  ? Math.round(((analytics.totalComments + analytics.totalHearts) / analytics.totalViews) * 100)
                  : 0}%
              </div>
              <p className="text-xs dashboard-muted mt-1">
                Interaction rate
              </p>
            </DashboardCardContent>
          </DashboardCard>
        </div>
      )}

      {/* Content Stats */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <DashboardCard key={i}>
              <DashboardCardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
                  <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
                </div>
              </DashboardCardContent>
            </DashboardCard>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Blog Posts */}
          <DashboardCard className="hover:shadow-md transition-shadow duration-200">
            <DashboardCardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <DocumentTextIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <DashboardCardTitle className="text-lg">Blog Posts</DashboardCardTitle>
                  <DashboardCardDescription>{stats.publishedPosts} published</DashboardCardDescription>
                </div>
              </div>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="text-3xl font-bold mb-4 dashboard-text">{stats.totalPosts}</div>
              <Button asChild variant="outline" className="w-full">
                <Link href="/dashboard/posts" className="dashboard-link flex items-center gap-2">
                  Manage posts
                  <ArrowRightIcon className="w-4 h-4" />
                </Link>
              </Button>
            </DashboardCardContent>
          </DashboardCard>

          {/* Projects */}
          <DashboardCard className="hover:shadow-md transition-shadow duration-200">
            <DashboardCardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg flex items-center justify-center">
                  <RocketLaunchIcon className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div>
                  <DashboardCardTitle className="text-lg">Projects</DashboardCardTitle>
                  <DashboardCardDescription>{stats.publishedProjects} published</DashboardCardDescription>
                </div>
              </div>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="text-3xl font-bold mb-4 dashboard-text">{stats.totalProjects}</div>
              <Button asChild variant="outline" className="w-full">
                <Link href="/dashboard/projects" className="dashboard-link flex items-center gap-2">
                  Manage projects
                  <ArrowRightIcon className="w-4 h-4" />
                </Link>
              </Button>
            </DashboardCardContent>
          </DashboardCard>

          {/* Media Files */}
          <DashboardCard className="hover:shadow-md transition-shadow duration-200">
            <DashboardCardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-violet-100 dark:bg-violet-900/20 rounded-lg flex items-center justify-center">
                  <PhotoIcon className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                </div>
                <div>
                  <DashboardCardTitle className="text-lg">Media Files</DashboardCardTitle>
                  <DashboardCardDescription>Images & documents</DashboardCardDescription>
                </div>
              </div>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="text-3xl font-bold mb-4 dashboard-text">{stats.totalFiles}</div>
              <Button asChild variant="outline" className="w-full">
                <Link href="/dashboard/media" className="dashboard-link flex items-center gap-2">
                  Manage media
                  <ArrowRightIcon className="w-4 h-4" />
                </Link>
              </Button>
            </DashboardCardContent>
          </DashboardCard>
        </div>
      )}

      <Separator />

      {/* Quick Actions */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle className="flex items-center gap-2">
            <PlusIcon className="w-5 h-5" />
            Quick Actions
          </DashboardCardTitle>
          <DashboardCardDescription>
            Create new content or access key features
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button asChild variant="outline" className="h-20 flex-col gap-2">
              <Link href="/dashboard/posts/new" className="dashboard-link">
                <DocumentTextIcon className="w-6 h-6" />
                New Blog Post
              </Link>
            </Button>

            <Button asChild variant="outline" className="h-20 flex-col gap-2">
              <Link href="/dashboard/projects/new" className="dashboard-link">
                <RocketLaunchIcon className="w-6 h-6" />
                New Project
              </Link>
            </Button>

            <Button asChild variant="outline" className="h-20 flex-col gap-2">
              <Link href="/dashboard/media" className="dashboard-link">
                <PhotoIcon className="w-6 h-6" />
                Upload Media
              </Link>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex-col gap-2"
              onClick={() => setShowAnalytics(true)}
            >
              <ChartBarIcon className="w-6 h-6" />
              View Analytics
            </Button>
          </div>
        </DashboardCardContent>
      </DashboardCard>


    </div>
  )
}
