import { ContactFormData, ProjectInquiryData } from '@/types'
import emailjs from '@emailjs/browser'

// EmailJS configuration
const EMAILJS_SERVICE_ID = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'YOUR_SERVICE_ID'
const EMAILJS_TEMPLATE_ID_CONTACT = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_CONTACT || 'YOUR_CONTACT_TEMPLATE_ID'
const EMAILJS_TEMPLATE_ID_PROJECT = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_PROJECT || 'YOUR_PROJECT_TEMPLATE_ID'
const EMAILJS_PUBLIC_KEY = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'YOUR_PUBLIC_KEY'

// Initialize EmailJS
if (typeof window !== 'undefined') {
  emailjs.init(EMAILJS_PUBLIC_KEY)
}

// Get user's location and currency based on IP
export async function getUserLocation(): Promise<{ country: string; currency: string }> {
  // Map common countries to their currencies
  const currencyMap: { [key: string]: string } = {
    'US': 'USD',
    'CA': 'CAD',
    'GB': 'GBP',
    'AU': 'AUD',
    'DE': 'EUR',
    'FR': 'EUR',
    'IT': 'EUR',
    'ES': 'EUR',
    'NL': 'EUR',
    'JP': 'JPY',
    'CN': 'CNY',
    'IN': 'INR',
    'BR': 'BRL',
    'MX': 'MXN',
    'PH': 'PHP',
    'SG': 'SGD',
    'MY': 'MYR',
    'TH': 'THB',
    'ID': 'IDR',
    'VN': 'VND',
  }

  try {
    // Use a more reliable service with timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 second timeout

    const response = await fetch('https://ipapi.co/json/', {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
      }
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    // Check if we got valid data
    if (data.country_code) {
      const currency = currencyMap[data.country_code] || 'USD'
      return {
        country: data.country_name || 'Unknown',
        currency
      }
    }
  } catch (error) {
    // Silently handle errors - don't log to console to avoid spam
    // This is expected to fail sometimes due to CORS, rate limits, etc.
  }

  // Default fallback - no console logs to avoid spam
  return { country: 'Philippines', currency: 'PHP' }
}

// Send contact form email
export async function sendContactEmail(data: ContactFormData): Promise<boolean> {
  try {
    const templateParams = {
      to_email: '<EMAIL>',
      from_name: data.name,
      from_email: data.email,
      subject: data.subject,
      message: data.message,
      reply_to: data.email,
    }

    const result = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID_CONTACT,
      templateParams
    )

    if (process.env.NODE_ENV === 'development') {
      console.log('Contact email sent successfully:', result)
    }
    return true
  } catch (error) {
    console.error('Error sending contact email:', error)
    return false
  }
}

// Send project inquiry email
export async function sendProjectInquiryEmail(data: ProjectInquiryData): Promise<boolean> {
  try {
    const templateParams = {
      to_email: '<EMAIL>',
      from_name: data.name,
      from_email: data.email,
      phone: data.phone || 'Not provided',
      company: data.company || 'Not provided',
      website: data.website || 'Not provided',
      project_title: data.title,
      service: data.service,
      budget: data.budget || 'Not specified',
      deadline: data.deadline || 'Not specified',
      hear_about: data.hearAbout || 'Not specified',
      project_description: data.description,
      reply_to: data.email,
    }

    const result = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID_PROJECT,
      templateParams
    )

    if (process.env.NODE_ENV === 'development') {
      console.log('Project inquiry email sent successfully:', result)
    }
    return true
  } catch (error) {
    console.error('Error sending project inquiry email:', error)
    return false
  }
}

// Format currency for display
export function formatCurrency(amount: string, currency: string): string {
  const currencySymbols: { [key: string]: string } = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CAD': 'C$',
    'AUD': 'A$',
    'CNY': '¥',
    'INR': '₹',
    'BRL': 'R$',
    'MXN': '$',
    'PHP': '₱',
    'SGD': 'S$',
    'MYR': 'RM',
    'THB': '฿',
    'IDR': 'Rp',
    'VND': '₫',
  }

  const symbol = currencySymbols[currency] || currency
  return `${symbol}${amount}`
}

// Get budget ranges for different currencies
export function getBudgetRanges(currency: string): string[] {
  const ranges: { [key: string]: string[] } = {
    'USD': ['< $5,000', '$5,000 - $15,000', '$15,000 - $50,000', '$50,000+'],
    'EUR': ['< €4,500', '€4,500 - €13,500', '€13,500 - €45,000', '€45,000+'],
    'GBP': ['< £4,000', '£4,000 - £12,000', '£12,000 - £40,000', '£40,000+'],
    'CAD': ['< C$6,500', 'C$6,500 - C$20,000', 'C$20,000 - C$65,000', 'C$65,000+'],
    'AUD': ['< A$7,500', 'A$7,500 - A$22,500', 'A$22,500 - A$75,000', 'A$75,000+'],
    'PHP': ['< ₱250,000', '₱250,000 - ₱750,000', '₱750,000 - ₱2,500,000', '₱2,500,000+'],
    'SGD': ['< S$7,000', 'S$7,000 - S$21,000', 'S$21,000 - S$70,000', 'S$70,000+'],
    'MYR': ['< RM20,000', 'RM20,000 - RM60,000', 'RM60,000 - RM200,000', 'RM200,000+'],
    'THB': ['< ฿175,000', '฿175,000 - ฿525,000', '฿525,000 - ฿1,750,000', '฿1,750,000+'],
    'IDR': ['< Rp75,000,000', 'Rp75,000,000 - Rp225,000,000', 'Rp225,000,000 - Rp750,000,000', 'Rp750,000,000+'],
    'VND': ['< ₫120,000,000', '₫120,000,000 - ₫360,000,000', '₫360,000,000 - ₫1,200,000,000', '₫1,200,000,000+'],
  }

  return ranges[currency] || ranges['USD']
}
